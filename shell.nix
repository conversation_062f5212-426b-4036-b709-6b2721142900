{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    # Build tools
    gcc
    gnumake
    cmake
    pkg-config
    file
    
    # Graphics libraries
    glfw
    libGL
    libGLU
    
    # X11 libraries
    xorg.libX11
    xorg.libXrandr
    xorg.libXinerama
    xorg.libXcursor
    xorg.libXi
    xorg.libXext

    # Audio libraries
    # Note: BASS libraries need to be manually installed
    # For now, we'll add ALSA for basic audio support
    alsa-lib
    
    # Development tools
    gdb
    valgrind
    
    # xmake
    xmake
  ];

  shellHook = ''
    echo "Development environment for Piano Keyboard project"
    echo "Available tools: gcc, make, cmake, pkg-config, xmake"
    echo "Graphics libraries: GLFW, OpenGL"
    echo "Run 'xmake' to build the project"
  '';
}
