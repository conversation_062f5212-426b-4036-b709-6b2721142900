#include "audio_limiter.h"
#include <iostream>
#include <cstring>

AudioLimiter::AudioLimiter()
    : sample_rate_(44100.0f)
    , channels_(2)
    , enabled_(true)
    , threshold_db_(-6.0f)
    , release_ms_(100.0f)
    , lookahead_ms_(5.0f)
    , threshold_(0.5f)
    , release_coef_(0.0f)
    , lookahead_coef_(0.0f)
    , peak_envelope_(0.0f)
    , smoothed_gain_(1.0f)
    , gain_reduction_db_(0.0f)
    , peak_level_db_(-60.0f)
    , output_level_db_(-60.0f)
    , lookahead_samples_(0)
    , buffer_write_pos_(0)
    , buffer_read_pos_(0)
    , peak_buffer_size_(1024)
    , peak_write_pos_(0)
{
    peak_buffer_.resize(peak_buffer_size_, 0.0f);
}

AudioLimiter::~AudioLimiter() {
}

void AudioLimiter::Initialize(float sample_rate, int channels) {
    sample_rate_ = sample_rate;
    channels_ = channels;

    // Calculate look-ahead buffer size
    lookahead_samples_ = static_cast<int>(lookahead_ms_ * sample_rate_ / 1000.0f);
    lookahead_buffer_.resize(lookahead_samples_ * channels_, 0.0f);

    buffer_write_pos_ = 0;
    buffer_read_pos_ = 0;

    UpdateCoefficients();
    Reset();

    std::cout << "AudioLimiter initialized: " << sample_rate_ << "Hz, "
              << channels_ << " channels, " << lookahead_samples_ << " lookahead samples" << std::endl;
}

void AudioLimiter::ProcessSamples(float* samples, int sample_count) {
    if (!enabled_ || sample_count <= 0) {
        return;
    }

    for (int i = 0; i < sample_count; ++i) {
        // Process each channel
        for (int ch = 0; ch < channels_; ++ch) {
            int sample_idx = i * channels_ + ch;

            float sample = samples[sample_idx];

            // 1. Get Absolute Value
            float abs_sample = std::abs(sample);

            // 2. Update Peak Envelope (Lookahead Smoothing)
            peak_envelope_ = peak_envelope_ * lookahead_coef_; // Decay the envelope
            if (abs_sample > peak_envelope_) {
                peak_envelope_ = abs_sample; // Instantaneous attack if current sample is higher
            }

            // 3. Calculate Instantaneous Target Gain
            float inst_gain;
            if (peak_envelope_ <= threshold_) {
                // Envelope is at or below the threshold, target gain is 1.0 (no reduction)
                inst_gain = 1.0f;
            } else {
                // Envelope is above the threshold, calculate required gain reduction
                // Avoid division by zero (should not happen if threshold > 0 and peak_envelope > threshold)
                inst_gain = threshold_ / peak_envelope_;
            }

            // 4. Apply Gain Smoothing
            if (inst_gain < smoothed_gain_) {
                // Attack: Apply gain reduction immediately
                smoothed_gain_ = inst_gain;
            } else {
                // Release: Recover gain smoothly towards the target instantaneous gain
                smoothed_gain_ = release_coef_ * smoothed_gain_ + (1.0f - release_coef_) * inst_gain;
            }

            // 5. Generate Output Sample
            float output_sample = sample * smoothed_gain_;

            samples[sample_idx] = output_sample;

            // Update monitoring
            UpdatePeakLevel(abs_sample);
            UpdateOutputLevel(std::abs(output_sample));
        }
    }

    // Update gain reduction display value
    gain_reduction_db_ = LinearToDb(smoothed_gain_);
}

void AudioLimiter::SetThreshold(float threshold_db) {
    threshold_db_ = std::max(-60.0f, std::min(0.0f, threshold_db));
    UpdateCoefficients();
}

void AudioLimiter::SetReleaseTime(float release_ms) {
    release_ms_ = std::max(5.0f, std::min(200.0f, release_ms));
    UpdateCoefficients();
}

void AudioLimiter::SetLookAheadTime(float lookahead_ms) {
    lookahead_ms_ = std::max(5.0f, std::min(20.0f, lookahead_ms));
    UpdateCoefficients();
}

void AudioLimiter::Reset() {
    peak_envelope_ = 0.0f;
    smoothed_gain_ = 1.0f;
    gain_reduction_db_ = 0.0f;
    peak_level_db_ = -60.0f;
    output_level_db_ = -60.0f;
    buffer_write_pos_ = 0;
    buffer_read_pos_ = 0;
    peak_write_pos_ = 0;

    // Clear buffers
    std::fill(lookahead_buffer_.begin(), lookahead_buffer_.end(), 0.0f);
    std::fill(peak_buffer_.begin(), peak_buffer_.end(), 0.0f);
}

void AudioLimiter::UpdateCoefficients() {
    // 1. Calculate Linear Threshold from threshold_db
    threshold_ = std::pow(10.0f, threshold_db_ / 20.0f);

    // 2. Calculate Release Coefficient
    if (sample_rate_ > 0 && release_ms_ > 0) {
        release_coef_ = std::exp(-1.0f / (release_ms_ / 1000.0f * sample_rate_));
        // Clamp to safe range
        release_coef_ = std::max(0.0f, std::min(0.999f, release_coef_));
    }

    // 3. Calculate Lookahead Coefficient
    if (sample_rate_ > 0 && lookahead_ms_ > 0) {
        lookahead_coef_ = std::exp(-1.0f / (lookahead_ms_ / 1000.0f * sample_rate_));
        // Clamp to safe range
        lookahead_coef_ = std::max(0.0f, std::min(0.999f, lookahead_coef_));
    }
}

float AudioLimiter::LinearToDb(float linear) const {
    if (linear <= 1e-10f) return -200.0f;  // Prevent extreme negative values
    float db = 20.0f * std::log10f(linear);
    return std::max(-200.0f, std::min(200.0f, db));  // Clamp to reasonable range
}

float AudioLimiter::DbToLinear(float db) const {
    // Clamp dB values to prevent extreme results
    db = std::max(-200.0f, std::min(200.0f, db));
    return std::pow(10.0f, db / 20.0f);
}

void AudioLimiter::UpdatePeakLevel(float sample) {
    // Store sample in peak buffer
    peak_buffer_[peak_write_pos_] = sample;
    peak_write_pos_ = (peak_write_pos_ + 1) % peak_buffer_size_;
    
    // Find peak in buffer
    float peak = 0.0f;
    for (float val : peak_buffer_) {
        peak = std::max(peak, val);
    }
    
    peak_level_db_ = LinearToDb(peak);
}

void AudioLimiter::UpdateOutputLevel(float sample) {
    // Simple peak hold for output level
    static float output_peak = 0.0f;
    static int hold_counter = 0;
    
    output_peak = std::max(output_peak, sample);
    
    if (++hold_counter >= static_cast<int>(sample_rate_ * 0.1f)) { // 100ms hold
        output_level_db_ = LinearToDb(output_peak);
        output_peak *= 0.9f; // Slow decay
        hold_counter = 0;
    }
}
