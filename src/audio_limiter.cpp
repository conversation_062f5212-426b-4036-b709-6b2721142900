#include "audio_limiter.h"
#include <algorithm> // for std::max
#include <stdexcept> // for std::invalid_argument
#include <iostream>

// Default constructor for backward compatibility
AudioLimiter::AudioLimiter()
    : m_sample_rate(44100.0f),
      m_threshold_db(-6.0f),
      m_release_time_ms(100.0f),
      m_lookahead_ms(5.0f),
      m_enabled(true),
      m_channels(2),
      m_gain_reduction_db(0.0f),
      m_peak_level_db(-60.0f),
      m_output_level_db(-60.0f)
{
    updateCoefficients();
    reset();
}

AudioLimiter::AudioLimiter(float sample_rate, float threshold_db, float release_time_ms, float lookahead_ms)
    : m_sample_rate(sample_rate),
      m_threshold_db(threshold_db),
      m_release_time_ms(release_time_ms),
      m_lookahead_ms(lookahead_ms),
      m_enabled(true),
      m_channels(2),
      m_gain_reduction_db(0.0f),
      m_peak_level_db(-60.0f),
      m_output_level_db(-60.0f)
{
    if (m_sample_rate <= 0) {
        throw std::invalid_argument("Sample rate must be positive.");
    }
    updateCoefficients();
    reset();
}

void AudioLimiter::updateCoefficients() {
    // 1. 線形スレッショルドを計算
    m_threshold = std::pow(10.0f, m_threshold_db / 20.0f);

    // 2. リリース係数を計算 (0除算を避ける)
    float release_time_sec = m_release_time_ms / 1000.0f;
    if (release_time_sec > 0) {
        m_release_coef = std::exp(-1.0f / (release_time_sec * m_sample_rate));
    } else {
        m_release_coef = 0.0f; // 即時リリース
    }

    // 3. ルックアヘッド係数を計算 (0除算を避ける)
    float lookahead_time_sec = m_lookahead_ms / 1000.0f;
    if (lookahead_time_sec > 0) {
        m_lookahead_coef = std::exp(-1.0f / (lookahead_time_sec * m_sample_rate));
    } else {
        m_lookahead_coef = 0.0f; // ルックアヘッドなし
    }
}

void AudioLimiter::reset() {
    // 4. 状態変数を初期化
    m_peak_envelope = 0.0f;
    m_smoothed_gain = 1.0f;
}

float AudioLimiter::processSample(float sample) {
    // 1. 絶対値を取得
    const float abs_sample = std::abs(sample);

    // 2. ピークエンベロープを更新 (ルックアヘッド平滑化)
    // エンベロープを減衰させ、現在のサンプルがエンベロープより大きい場合は即座に更新 (高速アタック)
    // この実装は if 文のロジックと等価で、より簡潔です
    m_peak_envelope = std::max(abs_sample, m_peak_envelope * m_lookahead_coef);

    // 3. 瞬間的なターゲットゲインを計算
    float inst_gain;
    if (m_peak_envelope <= m_threshold) {
        inst_gain = 1.0f;
    } else {
        // 0除算を避ける (m_threshold > 0 かつ m_peak_envelope > m_threshold ならば安全)
        inst_gain = m_threshold / m_peak_envelope;
    }

    // 4. ゲインの平滑化を適用
    if (inst_gain < m_smoothed_gain) {
        // アタック: ゲインリダクションを即座に適用
        m_smoothed_gain = inst_gain;
    } else {
        // リリース: ゲインを滑らかに回復
        m_smoothed_gain = m_release_coef * m_smoothed_gain + (1.0f - m_release_coef) * inst_gain;
    }

    // 5. パフォーマンス監視変数を更新
    m_gain_reduction_db = -20.0f * std::log10(std::max(0.001f, m_smoothed_gain));
    m_peak_level_db = 20.0f * std::log10(std::max(0.001f, abs_sample));

    // 6. 出力サンプルを生成
    float output = sample * m_smoothed_gain;
    m_output_level_db = 20.0f * std::log10(std::max(0.001f, std::abs(output)));

    return output;
}

// --- セッター関数の実装 ---

void AudioLimiter::setThreshold(float threshold_db) {
    m_threshold_db = threshold_db;
    updateCoefficients();
}

void AudioLimiter::setReleaseTime(float release_time_ms) {
    m_release_time_ms = release_time_ms;
    updateCoefficients();
}

void AudioLimiter::setLookahead(float lookahead_ms) {
    m_lookahead_ms = lookahead_ms;
    updateCoefficients();
}

// Legacy interface implementation for backward compatibility
void AudioLimiter::Initialize(float sample_rate, int channels) {
    m_sample_rate = sample_rate;
    m_channels = channels;
    updateCoefficients();
    reset();
    std::cout << "AudioLimiter initialized: " << sample_rate << "Hz, "
              << channels << " channels" << std::endl;
}

void AudioLimiter::ProcessSamples(float* samples, int sample_count) {
    if (!m_enabled || sample_count <= 0) {
        return;
    }

    // Process samples (assuming stereo interleaved format)
    for (int i = 0; i < sample_count * m_channels; ++i) {
        samples[i] = processSample(samples[i]);
    }
}

bool AudioLimiter::IsEnabled() const {
    return m_enabled;
}

void AudioLimiter::SetEnabled(bool enabled) {
    m_enabled = enabled;
}

void AudioLimiter::SetThreshold(float threshold_db) {
    setThreshold(threshold_db);
}

void AudioLimiter::SetReleaseTime(float release_time_ms) {
    setReleaseTime(release_time_ms);
}

void AudioLimiter::SetLookAheadTime(float lookahead_ms) {
    setLookahead(lookahead_ms);
}

void AudioLimiter::Reset() {
    reset();
}

// Performance monitoring methods
float AudioLimiter::GetGainReduction() const {
    return m_gain_reduction_db;
}

float AudioLimiter::GetPeakLevel() const {
    return m_peak_level_db;
}

float AudioLimiter::GetOutputLevel() const {
    return m_output_level_db;
}