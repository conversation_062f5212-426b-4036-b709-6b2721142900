#pragma once

#include <vector>
#include <queue>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <memory>
#include <unordered_map>
#include <chrono>
#include <string>

#if defined(_WIN32)
#include <windows.h>
#else
#include <dlfcn.h>
#endif

#define NOBASSMIDIOVERLOADS
#include "bass.h"
#include "bassmidi.h"

// Forward declaration
class PianoKeyboard;

// MIDI event structure for thread-safe processing
struct MIDIEvent {
    enum Type {
        NOTE_ON,
        NOTE_OFF,
        CONTROL_CHANGE,
        PROGRAM_CHANGE,
        STOP_ALL_NOTES
    };
    
    Type type;
    int note;
    int velocity;
    int channel;
    int controller;
    int value;
    std::chrono::high_resolution_clock::time_point timestamp;
    
    MIDIEvent() : type(NOTE_ON), note(0), velocity(0), channel(0), controller(0), value(0) {
        timestamp = std::chrono::high_resolution_clock::now();
    }
};

// MIDI stream wrapper for thread-safe operations
class MIDIStream {
public:
    MIDIStream();
    ~MIDIStream();
    
    bool Initialize(int stream_id, HSOUNDFONT soundfont);
    void Cleanup();
    
    bool ProcessEvent(const MIDIEvent& event);
    void SetVolume(float volume);
    
    int GetActiveNotes() const { return active_notes_.load(); }
    int GetStreamId() const { return stream_id_; }
    bool IsInitialized() const { return initialized_; }
    
    // Thread-safe note tracking
    void AddActiveNote(int note);
    void RemoveActiveNote(int note);
    void ClearActiveNotes();
    
private:
    HSTREAM midi_stream_;
    int stream_id_;
    std::atomic<bool> initialized_;
    std::atomic<int> active_notes_;
    float volume_;
    
    // Thread-safe note tracking
    mutable std::mutex notes_mutex_;
    std::unordered_map<int, int> active_note_map_; // note -> count
    
    // BASS function pointers (shared from main engine)
    static decltype(&BASS_MIDI_StreamCreate) BASS_MIDI_StreamCreate_ptr;
    static decltype(&BASS_MIDI_StreamEvent) BASS_MIDI_StreamEvent_ptr;
    static decltype(&BASS_ChannelPlay) BASS_ChannelPlay_ptr;
    static decltype(&BASS_ChannelSetAttribute) BASS_ChannelSetAttribute_ptr;
    static decltype(&BASS_StreamFree) BASS_StreamFree_ptr;
    static decltype(&BASS_MIDI_StreamSetFonts) BASS_MIDI_StreamSetFonts_ptr;
    
public:
    // Static method to set function pointers
    static void SetBASSFunctionPointers(
        decltype(&BASS_MIDI_StreamCreate) create_ptr,
        decltype(&BASS_MIDI_StreamEvent) event_ptr,
        decltype(&BASS_ChannelPlay) play_ptr,
        decltype(&BASS_ChannelSetAttribute) attr_ptr,
        decltype(&BASS_StreamFree) free_ptr,
        decltype(&BASS_MIDI_StreamSetFonts) set_fonts_ptr
    );
};

// Worker thread for processing MIDI events
class MIDIWorkerThread {
public:
    MIDIWorkerThread(int thread_id);
    ~MIDIWorkerThread();
    
    bool Start();
    void Stop();
    void AddStream(std::shared_ptr<MIDIStream> stream);
    void RemoveStream(int stream_id);
    
    bool QueueEvent(const MIDIEvent& event, int preferred_stream_id = -1);
    
    int GetQueueSize() const { return event_queue_.size(); }
    int GetStreamCount() const { return streams_.size(); }
    
private:
    void WorkerLoop();
    std::shared_ptr<MIDIStream> SelectBestStream(const MIDIEvent& event, int preferred_stream_id);
    
    int thread_id_;
    std::atomic<bool> running_;
    std::unique_ptr<std::thread> worker_thread_;
    
    // Thread-safe event queue
    mutable std::mutex queue_mutex_;
    std::condition_variable queue_condition_;
    std::queue<std::pair<MIDIEvent, int>> event_queue_; // event, preferred_stream_id
    
    // Stream management
    mutable std::mutex streams_mutex_;
    std::vector<std::shared_ptr<MIDIStream>> streams_;
    std::unordered_map<int, std::shared_ptr<MIDIStream>> stream_map_;
};

// Main multithreaded MIDI engine
class MultithreadedMIDIEngine {
public:
    // Advanced load balancing
    enum class LoadBalancingStrategy {
        ROUND_ROBIN,
        LEAST_LOADED,
        NOTE_AFFINITY,
        HYBRID
    };

    MultithreadedMIDIEngine();
    ~MultithreadedMIDIEngine();

    bool Initialize(int num_threads = 4, int streams_per_thread = 2);
    void Cleanup();
    
    bool LoadSoundfont(const std::string& soundfont_path);
    
    // Main interface for playing notes
    void PlayNote(int note, int velocity = 127, int channel = 0);
    void StopNote(int note, int channel = 0);
    void StopAllNotes();
    
    // Control changes
    void SendControlChange(int controller, int value, int channel = 0);
    void SendProgramChange(int program, int channel = 0);
    
    // Volume control
    void SetMasterVolume(float volume);
    float GetMasterVolume() const { return master_volume_; }
    
    // Statistics and monitoring
    int GetTotalActiveNotes() const;
    int GetTotalQueuedEvents() const;
    float GetAverageLatency() const;

    // Load balancing configuration
    void SetLoadBalancingStrategy(LoadBalancingStrategy strategy);
    LoadBalancingStrategy GetLoadBalancingStrategy() const { return load_balancing_strategy_; }

    // Performance monitoring
    struct PerformanceStats {
        int total_active_notes;
        int total_queued_events;
        float average_latency_ms;
        std::vector<int> thread_loads; // active notes per thread
        std::vector<int> queue_sizes;  // queue size per thread
    };

    PerformanceStats GetPerformanceStats() const;
    
    // Piano keyboard integration
    void SetPianoKeyboard(PianoKeyboard* piano_keyboard);
    
    bool IsInitialized() const { return initialized_; }
    
private:
    bool initialized_;
    int num_threads_;
    int streams_per_thread_;
    
    HSOUNDFONT soundfont_;
    std::string current_soundfont_path_;
    std::atomic<float> master_volume_;
    
    // Worker threads
    std::vector<std::unique_ptr<MIDIWorkerThread>> worker_threads_;
    
    // Load balancing
    std::atomic<int> round_robin_counter_;
    LoadBalancingStrategy load_balancing_strategy_;
    std::unordered_map<int, int> note_to_thread_affinity_; // note -> preferred thread
    mutable std::mutex affinity_mutex_;
    
    // Piano keyboard for visual feedback
    PianoKeyboard* piano_keyboard_;
    
    // Performance monitoring
    mutable std::mutex stats_mutex_;
    std::chrono::high_resolution_clock::time_point last_stats_update_;
    std::vector<std::chrono::duration<double>> latency_samples_;
    
    // Helper methods
    int SelectWorkerThread(int note);
    int SelectWorkerThreadRoundRobin();
    int SelectWorkerThreadLeastLoaded();
    int SelectWorkerThreadNoteAffinity(int note);
    int SelectWorkerThreadHybrid(int note);
    void UpdateStatistics();
    void NotifyMIDIKeyPressed(int note, bool pressed);
    void NotifyMIDIKeyPressed(int note, bool pressed, int channel);
    void UpdateNoteAffinity(int note, int thread_id);
    
    // BASS function pointers (will be set from AudioEngine)
    decltype(&BASS_MIDI_StreamCreate) BASS_MIDI_StreamCreate_ptr_;
    decltype(&BASS_MIDI_StreamEvent) BASS_MIDI_StreamEvent_ptr_;
    decltype(&BASS_ChannelPlay) BASS_ChannelPlay_ptr_;
    decltype(&BASS_ChannelSetAttribute) BASS_ChannelSetAttribute_ptr_;
    decltype(&BASS_StreamFree) BASS_StreamFree_ptr_;
    decltype(&BASS_MIDI_FontInit) BASS_MIDI_FontInit_ptr_;
    decltype(&BASS_MIDI_StreamSetFonts) BASS_MIDI_StreamSetFonts_ptr_;
    
public:
    // Method to set BASS function pointers from AudioEngine
    void SetBASSFunctionPointers(
        decltype(&BASS_MIDI_StreamCreate) create_ptr,
        decltype(&BASS_MIDI_StreamEvent) event_ptr,
        decltype(&BASS_ChannelPlay) play_ptr,
        decltype(&BASS_ChannelSetAttribute) attr_ptr,
        decltype(&BASS_StreamFree) free_ptr,
        decltype(&BASS_MIDI_FontInit) font_init_ptr,
        decltype(&BASS_MIDI_StreamSetFonts) set_fonts_ptr
    );
};
