#ifndef AUDIO_LIMITER_H
#define AUDIO_LIMITER_H

#include <cmath> // for exp, pow

class AudioLimiter {
public:
    // Default constructor for backward compatibility
    AudioLimiter();

    /**
     * @brief コンストラクタ
     * @param sample_rate サンプルレート (Hz)
     * @param threshold_db スレッショルド (dBFS)
     * @param release_time_ms リリースタイム (ms)
     * @param lookahead_ms ルックアヘッドタイム (ms)
     */
    AudioLimiter(float sample_rate, float threshold_db, float release_time_ms, float lookahead_ms);

    /**
     * @brief オーディオサンプルを1つ処理します
     * @param sample 入力サンプル
     * @return 処理後の出力サンプル
     */
    float processSample(float sample);

    /**
     * @brief リミッターの内部状態（エンベロープ、ゲイン）をリセットします
     */
    void reset();

    // --- パラメータ設定用のセッター関数 ---

    /**
     * @brief スレッショルドを更新します
     * @param threshold_db 新しいスレッショルド (dBFS)
     */
    void setThreshold(float threshold_db);

    /**
     * @brief リリースタイムを更新します
     * @param release_time_ms 新しいリリースタイム (ms)
     */
    void setReleaseTime(float release_time_ms);

    /**
     * @brief ルックアヘッドタイムを更新します
     * @param lookahead_ms 新しいルックアヘッドタイム (ms)
     */
    void setLookahead(float lookahead_ms);

    // Legacy interface for backward compatibility
    void Initialize(float sample_rate, int channels);
    void ProcessSamples(float* samples, int sample_count);
    bool IsEnabled() const;
    void SetEnabled(bool enabled);
    void SetThreshold(float threshold_db);
    void SetReleaseTime(float release_time_ms);
    void SetLookAheadTime(float lookahead_ms);
    void Reset();

    // Performance monitoring methods
    float GetGainReduction() const;
    float GetPeakLevel() const;
    float GetOutputLevel() const;

private:
    /**
     * @brief パラメータの変更に応じて内部係数を再計算します
     */
    void updateCoefficients();

    // ユーザー設定可能なパラメータ
    float m_sample_rate;
    float m_threshold_db;
    float m_release_time_ms;
    float m_lookahead_ms;

    // 内部状態変数とパラメータ
    float m_threshold;      // 線形スレッショルド
    float m_release_coef;   // リリース係数
    float m_lookahead_coef; // ルックアヘッド係数
    float m_peak_envelope;  // ピークエンベロープ
    float m_smoothed_gain;  // 平滑化されたゲイン

    // Legacy interface variables
    bool m_enabled;
    int m_channels;

    // Performance monitoring variables
    mutable float m_gain_reduction_db;
    mutable float m_peak_level_db;
    mutable float m_output_level_db;
};

#endif // AUDIO_LIMITER_H