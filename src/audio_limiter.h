#pragma once

#include <vector>
#include <cmath>
#include <algorithm>

/**
 * Digital Peak Limiter with Lookahead
 *
 * A digital audio limiter that prevents audio signals from exceeding a specified threshold.
 * Implements the algorithm described in the Limiter Implementation Guide.
 *
 * Features:
 * - Configurable threshold in dBFS
 * - Configurable release time
 * - Lookahead processing for transparent limiting
 * - Real-time gain reduction monitoring
 * - Multi-channel support
 */
class AudioLimiter {
public:
    AudioLimiter();
    ~AudioLimiter();

    // Initialize the limiter with sample rate and channel count
    void Initialize(float sample_rate, int channels = 2);

    // Process audio samples (interleaved format)
    void ProcessSamples(float* samples, int sample_count);

    // Parameter setters
    void SetThreshold(float threshold_db);      // Threshold in dBFS (-60.0 to 0.0)
    void SetReleaseTime(float release_ms);      // Release time in milliseconds (5.0 to 200.0)
    void SetLookAheadTime(float lookahead_ms);  // Look-ahead time in milliseconds (5.0 to 20.0)

    // Parameter getters
    float GetThreshold() const { return threshold_db_; }
    float GetReleaseTime() const { return release_ms_; }
    float GetLookAheadTime() const { return lookahead_ms_; }

    // Monitoring
    float GetGainReduction() const { return gain_reduction_db_; }  // Current gain reduction in dB
    float GetPeakLevel() const { return peak_level_db_; }          // Current peak level in dB
    float GetOutputLevel() const { return output_level_db_; }      // Current output level in dB

    // Enable/disable limiter
    void SetEnabled(bool enabled) { enabled_ = enabled; }
    bool IsEnabled() const { return enabled_; }

    // Reset internal state
    void Reset();

private:
    // Configuration
    float sample_rate_;
    int channels_;
    bool enabled_;

    // User-assignable parameters
    float threshold_db_;        // Threshold in dBFS
    float release_ms_;          // Release time in milliseconds
    float lookahead_ms_;        // Lookahead time in milliseconds

    // Internal state variables & parameters
    float threshold_;           // Linear amplitude threshold
    float release_coef_;        // Release coefficient
    float lookahead_coef_;      // Lookahead coefficient
    float peak_envelope_;       // Current peak envelope value
    float smoothed_gain_;       // Smoothed gain applied to output

    // Monitoring variables
    float gain_reduction_db_;   // Current gain reduction in dB
    float peak_level_db_;       // Current peak level in dB
    float output_level_db_;     // Current output level in dB

    // Look-ahead buffer (not used in this simplified implementation)
    std::vector<float> lookahead_buffer_;
    int lookahead_samples_;
    int buffer_write_pos_;
    int buffer_read_pos_;

    // Peak detection for monitoring
    std::vector<float> peak_buffer_;
    int peak_buffer_size_;
    int peak_write_pos_;

    // Helper functions
    void UpdateCoefficients();
    float LinearToDb(float linear) const;
    float DbToLinear(float db) const;
    void UpdatePeakLevel(float sample);
    void UpdateOutputLevel(float sample);
};
