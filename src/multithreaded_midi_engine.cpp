#include "multithreaded_midi_engine.h"
#include "piano_keyboard.h"
#include <iostream>
#include <algorithm>
#include <random>
#include <climits>

// Static member initialization for MIDIStream
decltype(&BASS_MIDI_StreamCreate) MIDIStream::BASS_MIDI_StreamCreate_ptr = nullptr;
decltype(&BASS_MIDI_StreamEvent) MIDIStream::BASS_MIDI_StreamEvent_ptr = nullptr;
decltype(&BASS_ChannelPlay) MIDIStream::BASS_ChannelPlay_ptr = nullptr;
decltype(&BASS_ChannelSetAttribute) MIDIStream::BASS_ChannelSetAttribute_ptr = nullptr;
decltype(&BASS_StreamFree) MIDIStream::BASS_StreamFree_ptr = nullptr;
decltype(&BASS_MIDI_StreamSetFonts) MIDIStream::BASS_MIDI_StreamSetFonts_ptr = nullptr;

// MIDIStream implementation
MIDIStream::MIDIStream()
    : midi_stream_(0)
    , stream_id_(-1)
    , initialized_(false)
    , active_notes_(0)
    , volume_(1.0f)
{
}

MIDIStream::~MIDIStream() {
    Cleanup();
}

bool MIDIStream::Initialize(int stream_id, HSOUNDFONT soundfont) {
    if (initialized_.load()) {
        return true;
    }
    
    if (!BASS_MIDI_StreamCreate_ptr || !BASS_ChannelPlay_ptr) {
        std::cerr << "BASS function pointers not set for MIDIStream" << std::endl;
        return false;
    }
    
    stream_id_ = stream_id;
    
    // Create MIDI stream
    midi_stream_ = BASS_MIDI_StreamCreate_ptr(16, BASS_SAMPLE_FLOAT, 0);
    if (midi_stream_ == 0) {
        std::cerr << "Failed to create MIDI stream " << stream_id_ << std::endl;
        return false;
    }
    
    // Set soundfont if provided
    if (soundfont != 0) {
        BASS_MIDI_FONT font;
        font.font = soundfont;
        font.preset = -1;
        font.bank = 0;
        
        if (BASS_MIDI_StreamSetFonts_ptr) {
            BASS_MIDI_StreamSetFonts_ptr(midi_stream_, &font, 1);
        }
    }
    
    // Start playback
    if (!BASS_ChannelPlay_ptr(midi_stream_, FALSE)) {
        std::cerr << "Failed to start MIDI stream " << stream_id_ << std::endl;
        if (BASS_StreamFree_ptr) {
            BASS_StreamFree_ptr(midi_stream_);
        }
        midi_stream_ = 0;
        return false;
    }
    
    // Set initial volume
    SetVolume(volume_);
    
    initialized_.store(true);
    std::cout << "MIDI stream " << stream_id_ << " initialized successfully" << std::endl;
    return true;
}

void MIDIStream::Cleanup() {
    if (initialized_.load()) {
        ClearActiveNotes();
        
        if (midi_stream_ != 0 && BASS_StreamFree_ptr) {
            BASS_StreamFree_ptr(midi_stream_);
            midi_stream_ = 0;
        }
        
        initialized_.store(false);
        std::cout << "MIDI stream " << stream_id_ << " cleaned up" << std::endl;
    }
}

bool MIDIStream::ProcessEvent(const MIDIEvent& event) {
    if (!initialized_.load() || !BASS_MIDI_StreamEvent_ptr) {
        return false;
    }
    
    switch (event.type) {
        case MIDIEvent::NOTE_ON: {
            unsigned int midi_param = MAKEWORD(event.note, event.velocity);
            if (BASS_MIDI_StreamEvent_ptr(midi_stream_, event.channel, MIDI_EVENT_NOTE, midi_param)) {
                AddActiveNote(event.note);
                return true;
            }
            break;
        }
        
        case MIDIEvent::NOTE_OFF: {
            unsigned int midi_param = MAKEWORD(event.note, 0);
            if (BASS_MIDI_StreamEvent_ptr(midi_stream_, event.channel, MIDI_EVENT_NOTE, midi_param)) {
                RemoveActiveNote(event.note);
                return true;
            }
            break;
        }
        
        case MIDIEvent::CONTROL_CHANGE: {
            unsigned int midi_param = MAKEWORD(event.controller, event.value);
            return BASS_MIDI_StreamEvent_ptr(midi_stream_, event.channel, MIDI_EVENT_CONTROL, midi_param);
        }
        
        case MIDIEvent::PROGRAM_CHANGE: {
            return BASS_MIDI_StreamEvent_ptr(midi_stream_, event.channel, MIDI_EVENT_PROGRAM, event.value);
        }
        
        case MIDIEvent::STOP_ALL_NOTES: {
            for (int channel = 0; channel < 16; ++channel) {
                BASS_MIDI_StreamEvent_ptr(midi_stream_, channel, MIDI_EVENT_NOTESOFF, 0);
            }
            ClearActiveNotes();
            return true;
        }
    }
    
    return false;
}

void MIDIStream::SetVolume(float volume) {
    volume_ = std::max(0.0f, std::min(1.0f, volume));
    
    if (initialized_.load() && BASS_ChannelSetAttribute_ptr) {
        BASS_ChannelSetAttribute_ptr(midi_stream_, BASS_ATTRIB_VOL, volume_);
    }
}

void MIDIStream::AddActiveNote(int note) {
    std::lock_guard<std::mutex> lock(notes_mutex_);
    active_note_map_[note]++;
    active_notes_.store(active_note_map_.size());
}

void MIDIStream::RemoveActiveNote(int note) {
    std::lock_guard<std::mutex> lock(notes_mutex_);
    auto it = active_note_map_.find(note);
    if (it != active_note_map_.end()) {
        it->second--;
        if (it->second <= 0) {
            active_note_map_.erase(it);
        }
    }
    active_notes_.store(active_note_map_.size());
}

void MIDIStream::ClearActiveNotes() {
    std::lock_guard<std::mutex> lock(notes_mutex_);
    active_note_map_.clear();
    active_notes_.store(0);
}

void MIDIStream::SetBASSFunctionPointers(
    decltype(&BASS_MIDI_StreamCreate) create_ptr,
    decltype(&BASS_MIDI_StreamEvent) event_ptr,
    decltype(&BASS_ChannelPlay) play_ptr,
    decltype(&BASS_ChannelSetAttribute) attr_ptr,
    decltype(&BASS_StreamFree) free_ptr,
    decltype(&BASS_MIDI_StreamSetFonts) set_fonts_ptr
) {
    BASS_MIDI_StreamCreate_ptr = create_ptr;
    BASS_MIDI_StreamEvent_ptr = event_ptr;
    BASS_ChannelPlay_ptr = play_ptr;
    BASS_ChannelSetAttribute_ptr = attr_ptr;
    BASS_StreamFree_ptr = free_ptr;
    BASS_MIDI_StreamSetFonts_ptr = set_fonts_ptr;
}

// MIDIWorkerThread implementation
MIDIWorkerThread::MIDIWorkerThread(int thread_id)
    : thread_id_(thread_id)
    , running_(false)
{
}

MIDIWorkerThread::~MIDIWorkerThread() {
    Stop();
}

bool MIDIWorkerThread::Start() {
    if (running_.load()) {
        return true;
    }
    
    running_.store(true);
    worker_thread_ = std::make_unique<std::thread>(&MIDIWorkerThread::WorkerLoop, this);
    
    std::cout << "MIDI worker thread " << thread_id_ << " started" << std::endl;
    return true;
}

void MIDIWorkerThread::Stop() {
    if (running_.load()) {
        running_.store(false);
        queue_condition_.notify_all();
        
        if (worker_thread_ && worker_thread_->joinable()) {
            worker_thread_->join();
        }
        
        worker_thread_.reset();
        std::cout << "MIDI worker thread " << thread_id_ << " stopped" << std::endl;
    }
}

void MIDIWorkerThread::AddStream(std::shared_ptr<MIDIStream> stream) {
    std::lock_guard<std::mutex> lock(streams_mutex_);
    streams_.push_back(stream);
    stream_map_[stream->GetStreamId()] = stream;
    std::cout << "Added stream " << stream->GetStreamId() << " to worker thread " << thread_id_ << std::endl;
}

void MIDIWorkerThread::RemoveStream(int stream_id) {
    std::lock_guard<std::mutex> lock(streams_mutex_);
    
    auto map_it = stream_map_.find(stream_id);
    if (map_it != stream_map_.end()) {
        stream_map_.erase(map_it);
        
        auto vec_it = std::find_if(streams_.begin(), streams_.end(),
            [stream_id](const std::shared_ptr<MIDIStream>& stream) {
                return stream->GetStreamId() == stream_id;
            });
        
        if (vec_it != streams_.end()) {
            streams_.erase(vec_it);
        }
        
        std::cout << "Removed stream " << stream_id << " from worker thread " << thread_id_ << std::endl;
    }
}

bool MIDIWorkerThread::QueueEvent(const MIDIEvent& event, int preferred_stream_id) {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    event_queue_.push(std::make_pair(event, preferred_stream_id));
    queue_condition_.notify_one();
    return true;
}

void MIDIWorkerThread::WorkerLoop() {
    while (running_.load()) {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        
        // Wait for events or stop signal
        queue_condition_.wait(lock, [this] {
            return !event_queue_.empty() || !running_.load();
        });
        
        if (!running_.load()) {
            break;
        }
        
        // Process all queued events
        while (!event_queue_.empty() && running_.load()) {
            auto event_pair = event_queue_.front();
            event_queue_.pop();
            lock.unlock();
            
            // Select best stream for this event
            auto stream = SelectBestStream(event_pair.first, event_pair.second);
            if (stream) {
                stream->ProcessEvent(event_pair.first);
            }
            
            lock.lock();
        }
    }
}

std::shared_ptr<MIDIStream> MIDIWorkerThread::SelectBestStream(const MIDIEvent& event, int preferred_stream_id) {
    std::lock_guard<std::mutex> lock(streams_mutex_);
    
    if (streams_.empty()) {
        return nullptr;
    }
    
    // Try preferred stream first
    if (preferred_stream_id >= 0) {
        auto it = stream_map_.find(preferred_stream_id);
        if (it != stream_map_.end() && it->second->IsInitialized()) {
            return it->second;
        }
    }
    
    // Find stream with least active notes
    std::shared_ptr<MIDIStream> best_stream = nullptr;
    int min_active_notes = INT_MAX;
    
    for (const auto& stream : streams_) {
        if (stream->IsInitialized()) {
            int active_notes = stream->GetActiveNotes();
            if (active_notes < min_active_notes) {
                min_active_notes = active_notes;
                best_stream = stream;
            }
        }
    }
    
    return best_stream;
}

// MultithreadedMIDIEngine implementation
MultithreadedMIDIEngine::MultithreadedMIDIEngine()
    : initialized_(false)
    , num_threads_(0)
    , streams_per_thread_(0)
    , soundfont_(0)
    , master_volume_(0.8f)
    , round_robin_counter_(0)
    , piano_keyboard_(nullptr)
    , load_balancing_strategy_(LoadBalancingStrategy::HYBRID)
    , BASS_MIDI_StreamCreate_ptr_(nullptr)
    , BASS_MIDI_StreamEvent_ptr_(nullptr)
    , BASS_ChannelPlay_ptr_(nullptr)
    , BASS_ChannelSetAttribute_ptr_(nullptr)
    , BASS_StreamFree_ptr_(nullptr)
    , BASS_MIDI_FontInit_ptr_(nullptr)
    , BASS_MIDI_StreamSetFonts_ptr_(nullptr)
{
    last_stats_update_ = std::chrono::high_resolution_clock::now();
}

MultithreadedMIDIEngine::~MultithreadedMIDIEngine() {
    Cleanup();
}

bool MultithreadedMIDIEngine::Initialize(int num_threads, int streams_per_thread) {
    if (initialized_) {
        return true;
    }

    if (num_threads <= 0 || streams_per_thread <= 0) {
        std::cerr << "Invalid thread/stream configuration" << std::endl;
        return false;
    }

    if (!BASS_MIDI_StreamCreate_ptr_ || !BASS_MIDI_StreamEvent_ptr_) {
        std::cerr << "BASS function pointers not set" << std::endl;
        return false;
    }

    num_threads_ = num_threads;
    streams_per_thread_ = streams_per_thread;

    // Set function pointers for MIDIStream
    MIDIStream::SetBASSFunctionPointers(
        BASS_MIDI_StreamCreate_ptr_,
        BASS_MIDI_StreamEvent_ptr_,
        BASS_ChannelPlay_ptr_,
        BASS_ChannelSetAttribute_ptr_,
        BASS_StreamFree_ptr_,
        BASS_MIDI_StreamSetFonts_ptr_
    );

    // Create worker threads
    worker_threads_.reserve(num_threads_);
    for (int i = 0; i < num_threads_; ++i) {
        auto worker = std::make_unique<MIDIWorkerThread>(i);

        // Create streams for this worker
        for (int j = 0; j < streams_per_thread_; ++j) {
            int stream_id = i * streams_per_thread_ + j;
            auto stream = std::make_shared<MIDIStream>();

            if (!stream->Initialize(stream_id, soundfont_)) {
                std::cerr << "Failed to initialize stream " << stream_id << std::endl;
                Cleanup();
                return false;
            }

            worker->AddStream(stream);
        }

        if (!worker->Start()) {
            std::cerr << "Failed to start worker thread " << i << std::endl;
            Cleanup();
            return false;
        }

        worker_threads_.push_back(std::move(worker));
    }

    initialized_ = true;
    std::cout << "Multithreaded MIDI engine initialized with " << num_threads_
              << " threads and " << streams_per_thread_ << " streams per thread" << std::endl;
    return true;
}

void MultithreadedMIDIEngine::Cleanup() {
    if (!initialized_) {
        return;
    }

    // Stop all worker threads
    for (auto& worker : worker_threads_) {
        worker->Stop();
    }
    worker_threads_.clear();

    initialized_ = false;
    std::cout << "Multithreaded MIDI engine cleaned up" << std::endl;
}

bool MultithreadedMIDIEngine::LoadSoundfont(const std::string& soundfont_path) {
    if (!BASS_MIDI_FontInit_ptr_) {
        std::cerr << "BASS_MIDI_FontInit function not available" << std::endl;
        return false;
    }

    // Free existing soundfont
    if (soundfont_ != 0) {
        // Note: We would need BASS_MIDI_FontFree here, but it's not in our function pointers
        soundfont_ = 0;
    }

    // Load new soundfont
    soundfont_ = BASS_MIDI_FontInit_ptr_(soundfont_path.c_str(), 0);
    if (soundfont_ == 0) {
        std::cerr << "Failed to load soundfont: " << soundfont_path << std::endl;
        return false;
    }

    current_soundfont_path_ = soundfont_path;

    // Update all existing streams with new soundfont
    if (initialized_) {
        // Note: This would require reinitializing all streams
        // For now, we'll just store the soundfont for new streams
    }

    std::cout << "Soundfont loaded: " << soundfont_path << std::endl;
    return true;
}

void MultithreadedMIDIEngine::PlayNote(int note, int velocity, int channel) {
    if (!initialized_) {
        return;
    }

    MIDIEvent event;
    event.type = MIDIEvent::NOTE_ON;
    event.note = note;
    event.velocity = velocity;
    event.channel = channel;

    // Select worker thread using load balancing
    int worker_index = SelectWorkerThread(note);
    if (worker_index >= 0 && worker_index < worker_threads_.size()) {
        worker_threads_[worker_index]->QueueEvent(event);

        // Notify piano keyboard for visual feedback
        NotifyMIDIKeyPressed(note, true, channel);
    }
}

void MultithreadedMIDIEngine::StopNote(int note, int channel) {
    if (!initialized_) {
        return;
    }

    MIDIEvent event;
    event.type = MIDIEvent::NOTE_OFF;
    event.note = note;
    event.velocity = 0;
    event.channel = channel;

    // Send to all worker threads to ensure note is stopped
    for (auto& worker : worker_threads_) {
        worker->QueueEvent(event);
    }

    // Notify piano keyboard for visual feedback
    NotifyMIDIKeyPressed(note, false, channel);
}

void MultithreadedMIDIEngine::StopAllNotes() {
    if (!initialized_) {
        return;
    }

    MIDIEvent event;
    event.type = MIDIEvent::STOP_ALL_NOTES;

    // Send to all worker threads
    for (auto& worker : worker_threads_) {
        worker->QueueEvent(event);
    }

    std::cout << "All notes stopped" << std::endl;
}

void MultithreadedMIDIEngine::SendControlChange(int controller, int value, int channel) {
    if (!initialized_) {
        return;
    }

    MIDIEvent event;
    event.type = MIDIEvent::CONTROL_CHANGE;
    event.controller = controller;
    event.value = value;
    event.channel = channel;

    // Send to all worker threads
    for (auto& worker : worker_threads_) {
        worker->QueueEvent(event);
    }
}

void MultithreadedMIDIEngine::SendProgramChange(int program, int channel) {
    if (!initialized_) {
        return;
    }

    MIDIEvent event;
    event.type = MIDIEvent::PROGRAM_CHANGE;
    event.value = program;
    event.channel = channel;

    // Send to all worker threads
    for (auto& worker : worker_threads_) {
        worker->QueueEvent(event);
    }
}

void MultithreadedMIDIEngine::SetMasterVolume(float volume) {
    master_volume_.store(std::max(0.0f, std::min(1.0f, volume)));

    // Update volume for all streams would require additional implementation
    // For now, we store the volume for new streams
}

int MultithreadedMIDIEngine::GetTotalActiveNotes() const {
    // This would require querying all streams across all worker threads
    // For now, return an estimate
    return 0;
}

int MultithreadedMIDIEngine::GetTotalQueuedEvents() const {
    int total = 0;
    for (const auto& worker : worker_threads_) {
        total += worker->GetQueueSize();
    }
    return total;
}

float MultithreadedMIDIEngine::GetAverageLatency() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);

    if (latency_samples_.empty()) {
        return 0.0f;
    }

    double total = 0.0;
    for (const auto& sample : latency_samples_) {
        total += sample.count();
    }

    return static_cast<float>(total / latency_samples_.size() * 1000.0); // Convert to milliseconds
}

void MultithreadedMIDIEngine::SetPianoKeyboard(PianoKeyboard* piano_keyboard) {
    piano_keyboard_ = piano_keyboard;
}

int MultithreadedMIDIEngine::SelectWorkerThread(int note) {
    switch (load_balancing_strategy_) {
        case LoadBalancingStrategy::ROUND_ROBIN:
            return SelectWorkerThreadRoundRobin();
        case LoadBalancingStrategy::LEAST_LOADED:
            return SelectWorkerThreadLeastLoaded();
        case LoadBalancingStrategy::NOTE_AFFINITY:
            return SelectWorkerThreadNoteAffinity(note);
        case LoadBalancingStrategy::HYBRID:
            return SelectWorkerThreadHybrid(note);
        default:
            return SelectWorkerThreadRoundRobin();
    }
}

int MultithreadedMIDIEngine::SelectWorkerThreadRoundRobin() {
    return round_robin_counter_.fetch_add(1) % num_threads_;
}

int MultithreadedMIDIEngine::SelectWorkerThreadLeastLoaded() {
    int best_thread = 0;
    int min_queue_size = INT_MAX;

    for (int i = 0; i < worker_threads_.size(); ++i) {
        int queue_size = worker_threads_[i]->GetQueueSize();
        if (queue_size < min_queue_size) {
            min_queue_size = queue_size;
            best_thread = i;
        }
    }

    return best_thread;
}

int MultithreadedMIDIEngine::SelectWorkerThreadNoteAffinity(int note) {
    std::lock_guard<std::mutex> lock(affinity_mutex_);

    auto it = note_to_thread_affinity_.find(note);
    if (it != note_to_thread_affinity_.end()) {
        return it->second;
    }

    // No affinity set, use round-robin and create affinity
    int thread_id = SelectWorkerThreadRoundRobin();
    note_to_thread_affinity_[note] = thread_id;
    return thread_id;
}

int MultithreadedMIDIEngine::SelectWorkerThreadHybrid(int note) {
    // Use note affinity for sustained notes, least loaded for new notes
    std::lock_guard<std::mutex> lock(affinity_mutex_);

    auto it = note_to_thread_affinity_.find(note);
    if (it != note_to_thread_affinity_.end()) {
        // Check if the affinity thread is not overloaded
        int affinity_thread = it->second;
        int affinity_queue_size = worker_threads_[affinity_thread]->GetQueueSize();

        // If affinity thread is heavily loaded, consider switching
        if (affinity_queue_size > 50) { // Threshold for switching
            int least_loaded = SelectWorkerThreadLeastLoaded();
            int least_loaded_queue_size = worker_threads_[least_loaded]->GetQueueSize();

            // Switch if the difference is significant
            if (least_loaded_queue_size < affinity_queue_size - 20) {
                note_to_thread_affinity_[note] = least_loaded;
                return least_loaded;
            }
        }

        return affinity_thread;
    }

    // No affinity, use least loaded
    int thread_id = SelectWorkerThreadLeastLoaded();
    note_to_thread_affinity_[note] = thread_id;
    return thread_id;
}

void MultithreadedMIDIEngine::SetLoadBalancingStrategy(LoadBalancingStrategy strategy) {
    load_balancing_strategy_ = strategy;
    std::cout << "Load balancing strategy changed to: " << static_cast<int>(strategy) << std::endl;
}

MultithreadedMIDIEngine::PerformanceStats MultithreadedMIDIEngine::GetPerformanceStats() const {
    PerformanceStats stats;

    stats.total_active_notes = GetTotalActiveNotes();
    stats.total_queued_events = GetTotalQueuedEvents();
    stats.average_latency_ms = GetAverageLatency();

    // Collect per-thread statistics
    stats.thread_loads.reserve(worker_threads_.size());
    stats.queue_sizes.reserve(worker_threads_.size());

    for (const auto& worker : worker_threads_) {
        stats.queue_sizes.push_back(worker->GetQueueSize());
        // Note: Getting active notes per thread would require additional implementation
        stats.thread_loads.push_back(0); // Placeholder
    }

    return stats;
}

void MultithreadedMIDIEngine::UpdateNoteAffinity(int note, int thread_id) {
    std::lock_guard<std::mutex> lock(affinity_mutex_);
    note_to_thread_affinity_[note] = thread_id;
}

void MultithreadedMIDIEngine::UpdateStatistics() {
    auto now = std::chrono::high_resolution_clock::now();
    std::lock_guard<std::mutex> lock(stats_mutex_);

    // Update statistics every second
    if (now - last_stats_update_ > std::chrono::seconds(1)) {
        // Clear old latency samples
        if (latency_samples_.size() > 1000) {
            latency_samples_.erase(latency_samples_.begin(),
                                 latency_samples_.begin() + latency_samples_.size() - 1000);
        }

        last_stats_update_ = now;
    }
}

void MultithreadedMIDIEngine::NotifyMIDIKeyPressed(int note, bool pressed) {
    // Call the overloaded version with channel -1 (no specific channel)
    NotifyMIDIKeyPressed(note, pressed, -1);
}

void MultithreadedMIDIEngine::NotifyMIDIKeyPressed(int note, bool pressed, int channel) {
    if (piano_keyboard_) {
        piano_keyboard_->SetMIDIKeyPressed(note, pressed, channel);
    }
}

void MultithreadedMIDIEngine::SetBASSFunctionPointers(
    decltype(&BASS_MIDI_StreamCreate) create_ptr,
    decltype(&BASS_MIDI_StreamEvent) event_ptr,
    decltype(&BASS_ChannelPlay) play_ptr,
    decltype(&BASS_ChannelSetAttribute) attr_ptr,
    decltype(&BASS_StreamFree) free_ptr,
    decltype(&BASS_MIDI_FontInit) font_init_ptr,
    decltype(&BASS_MIDI_StreamSetFonts) set_fonts_ptr
) {
    BASS_MIDI_StreamCreate_ptr_ = create_ptr;
    BASS_MIDI_StreamEvent_ptr_ = event_ptr;
    BASS_ChannelPlay_ptr_ = play_ptr;
    BASS_ChannelSetAttribute_ptr_ = attr_ptr;
    BASS_StreamFree_ptr_ = free_ptr;
    BASS_MIDI_FontInit_ptr_ = font_init_ptr;
    BASS_MIDI_StreamSetFonts_ptr_ = set_fonts_ptr;
}
