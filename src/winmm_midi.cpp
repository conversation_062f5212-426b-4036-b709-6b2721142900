#include "winmm_midi.h"
#include "alsa_midi.h"  // For MIDIMessage definition
#include <iostream>
#include <chrono>

// Link winmm library for MSVC (automatic linking)
#ifdef _MSC_VER
#pragma comment(lib, "winmm.lib")
#endif
// Note: For MinGW, winmm library is linked via xmake.lua configuration

WinMMMIDIInput::WinMMMIDIInput()
    : initialized_(false)
    , input_active_(false)
    , device_open_(false)
#ifdef _WIN32
    , midi_in_handle_(nullptr)
#endif
{
}

WinMMMIDIInput::~WinMMMIDIInput() {
    Cleanup();
}

bool WinMMMIDIInput::Initialize() {
#ifdef _WIN32
    if (initialized_) {
        return true;
    }
    
    std::cout << "Initializing Windows MIDI input system..." << std::endl;
    
    // Check if MIDI input devices are available
    UINT numDevices = midiInGetNumDevs();
    std::cout << "Found " << numDevices << " MIDI input devices" << std::endl;
    
    initialized_ = true;
    return true;
#else
    std::cerr << "Windows MIDI input not available on this platform" << std::endl;
    return false;
#endif
}

void WinMMMIDIInput::Cleanup() {
#ifdef _WIN32
    if (!initialized_) {
        return;
    }
    
    StopInput();
    CloseDevice();
    
    initialized_ = false;
    std::cout << "Windows MIDI input system cleaned up" << std::endl;
#endif
}

bool WinMMMIDIInput::IsInitialized() const {
    return initialized_;
}

std::vector<WinMMMIDIDevice> WinMMMIDIInput::GetInputDevices() const {
    std::vector<WinMMMIDIDevice> devices;
    
#ifdef _WIN32
    if (!initialized_) {
        return devices;
    }
    
    UINT numDevices = midiInGetNumDevs();
    
    for (UINT i = 0; i < numDevices; i++) {
        MIDIINCAPS caps;
        MMRESULT result = midiInGetDevCaps(i, &caps, sizeof(caps));
        
        if (result == MMSYSERR_NOERROR) {
            WinMMMIDIDevice device;
            device.device_id = i;
            device.name = std::string(caps.szPname);
            device.manufacturer = ""; // Windows API doesn't provide manufacturer info easily
            device.is_input = true;
            device.is_output = false;
            
            devices.push_back(device);
        }
    }
#endif
    
    return devices;
}

bool WinMMMIDIInput::OpenDevice(int device_id) {
#ifdef _WIN32
    if (!initialized_) {
        std::cerr << "MIDI input system not initialized" << std::endl;
        return false;
    }
    
    // Close existing device if open
    CloseDevice();
    
    // Open the MIDI input device
    MMRESULT result = midiInOpen(&midi_in_handle_, device_id, 
                                 reinterpret_cast<DWORD_PTR>(MidiInProc), 
                                 reinterpret_cast<DWORD_PTR>(this), 
                                 CALLBACK_FUNCTION);
    
    if (result != MMSYSERR_NOERROR) {
        std::cerr << "Failed to open MIDI input device " << device_id 
                  << " (error code: " << result << ")" << std::endl;
        return false;
    }
    
    // Get device information
    MIDIINCAPS caps;
    if (midiInGetDevCaps(device_id, &caps, sizeof(caps)) == MMSYSERR_NOERROR) {
        current_device_.device_id = device_id;
        current_device_.name = std::string(caps.szPname);
        current_device_.manufacturer = "";
        current_device_.is_input = true;
        current_device_.is_output = false;
    }
    
    device_open_ = true;
    std::cout << "Opened MIDI input device: " << current_device_.name << std::endl;
    return true;
#else
    return false;
#endif
}

void WinMMMIDIInput::CloseDevice() {
#ifdef _WIN32
    if (!device_open_ || !midi_in_handle_) {
        return;
    }
    
    StopInput();
    
    MMRESULT result = midiInClose(midi_in_handle_);
    if (result != MMSYSERR_NOERROR) {
        std::cerr << "Error closing MIDI input device (error code: " << result << ")" << std::endl;
    }
    
    midi_in_handle_ = nullptr;
    device_open_ = false;
    current_device_ = WinMMMIDIDevice();
    
    std::cout << "MIDI input device closed" << std::endl;
#endif
}

bool WinMMMIDIInput::IsDeviceOpen() const {
    return device_open_;
}

WinMMMIDIDevice WinMMMIDIInput::GetCurrentDevice() const {
    return current_device_;
}

bool WinMMMIDIInput::StartInput() {
#ifdef _WIN32
    if (!device_open_ || !midi_in_handle_) {
        std::cerr << "No MIDI input device open" << std::endl;
        return false;
    }
    
    if (input_active_) {
        return true; // Already started
    }
    
    MMRESULT result = midiInStart(midi_in_handle_);
    if (result != MMSYSERR_NOERROR) {
        std::cerr << "Failed to start MIDI input (error code: " << result << ")" << std::endl;
        return false;
    }
    
    input_active_ = true;
    std::cout << "MIDI input started" << std::endl;
    return true;
#else
    return false;
#endif
}

void WinMMMIDIInput::StopInput() {
#ifdef _WIN32
    if (!input_active_ || !midi_in_handle_) {
        return;
    }
    
    MMRESULT result = midiInStop(midi_in_handle_);
    if (result != MMSYSERR_NOERROR) {
        std::cerr << "Error stopping MIDI input (error code: " << result << ")" << std::endl;
    }
    
    // Reset the device to flush any pending messages
    midiInReset(midi_in_handle_);
    
    input_active_ = false;
    std::cout << "MIDI input stopped" << std::endl;
#endif
}

bool WinMMMIDIInput::IsInputActive() const {
    return input_active_;
}

void WinMMMIDIInput::SetMIDICallback(WinMMMIDIInputCallback callback) {
    midi_callback_ = callback;
}

#ifdef _WIN32
void CALLBACK WinMMMIDIInput::MidiInProc(HMIDIIN hMidiIn, UINT wMsg, DWORD_PTR dwInstance, DWORD_PTR dwParam1, DWORD_PTR dwParam2) {
    WinMMMIDIInput* instance = reinterpret_cast<WinMMMIDIInput*>(dwInstance);
    if (instance) {
        instance->HandleMidiMessage(wMsg, dwParam1, dwParam2);
    }
}

void WinMMMIDIInput::HandleMidiMessage(UINT wMsg, DWORD_PTR dwParam1, DWORD_PTR dwParam2) {
    switch (wMsg) {
        case MIM_DATA: {
            if (midi_callback_) {
                MIDIMessage message = ConvertToMIDIMessage(static_cast<DWORD>(dwParam1), static_cast<DWORD>(dwParam2));
                if (message.status != 0) { // Only process valid messages
                    midi_callback_(message);
                }
            }
            break;
        }
        case MIM_OPEN:
            std::cout << "MIDI input device opened" << std::endl;
            break;
        case MIM_CLOSE:
            std::cout << "MIDI input device closed" << std::endl;
            break;
        case MIM_ERROR:
            std::cerr << "MIDI input error" << std::endl;
            break;
        case MIM_LONGERROR:
            std::cerr << "MIDI input long error" << std::endl;
            break;
    }
}

MIDIMessage WinMMMIDIInput::ConvertToMIDIMessage(DWORD midiMessage, DWORD timestamp) {
    MIDIMessage message;
    message.status = static_cast<unsigned char>(midiMessage & 0xFF);
    message.data1 = static_cast<unsigned char>((midiMessage >> 8) & 0xFF);
    message.data2 = static_cast<unsigned char>((midiMessage >> 16) & 0xFF);
    message.timestamp = GetCurrentTimestamp();
    
    return message;
}

double WinMMMIDIInput::GetCurrentTimestamp() const {
    auto now = std::chrono::high_resolution_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration<double>(duration).count();
}
#endif
