#include "bass_multisynth.h"
#include "piano_keyboard.h"
#include <iostream>
#include <algorithm>
#include <climits>

// Static member initialization for BASSMIDISynth
decltype(&BASS_MIDI_StreamCreate) BASSMIDISynth::BASS_MIDI_StreamCreate_ptr = nullptr;
decltype(&BASS_MIDI_StreamEvent) BASSMIDISynth::BASS_MIDI_StreamEvent_ptr = nullptr;
decltype(&BASS_ChannelPlay) BASSMIDISynth::BASS_ChannelPlay_ptr = nullptr;
decltype(&BASS_ChannelSetAttribute) BASSMIDISynth::BASS_ChannelSetAttribute_ptr = nullptr;
decltype(&BASS_StreamFree) BASSMIDISynth::BASS_StreamFree_ptr = nullptr;
decltype(&BASS_MIDI_StreamSetFonts) BASSMIDISynth::BASS_MIDI_StreamSetFonts_ptr = nullptr;

// BASSMIDISynth implementation
BASSMIDISynth::BASSMIDISynth()
    : midi_stream_(0)
    , instance_id_(-1)
    , max_voices_(0)
    , current_voices_(0)
    , volume_(1.0f)
    , initialized_(false)
    , last_render_time_(std::chrono::duration<double>::zero())
{
}

BASSMIDISynth::~BASSMIDISynth() {
    Cleanup();
}

bool BASSMIDISynth::Initialize(int instance_id, uint32_t max_voices, HSOUNDFONT soundfont) {
    if (initialized_) {
        return true;
    }
    
    if (!BASS_MIDI_StreamCreate_ptr || !BASS_ChannelPlay_ptr) {
        std::cerr << "BASS function pointers not set for BASSMIDISynth" << std::endl;
        return false;
    }
    
    instance_id_ = instance_id;
    max_voices_ = max_voices;
    
    // Create MIDI stream with specified voice limit
    midi_stream_ = BASS_MIDI_StreamCreate_ptr(16, BASS_SAMPLE_FLOAT, 0);
    if (midi_stream_ == 0) {
        std::cerr << "Failed to create MIDI stream for instance " << instance_id_ << std::endl;
        return false;
    }
    
    // Set soundfont if provided
    if (soundfont != 0 && BASS_MIDI_StreamSetFonts_ptr) {
        BASS_MIDI_FONT font;
        font.font = soundfont;
        font.preset = -1;
        font.bank = 0;
        
        BASS_MIDI_StreamSetFonts_ptr(midi_stream_, &font, 1);
    }
    
    // Start playback
    if (!BASS_ChannelPlay_ptr(midi_stream_, FALSE)) {
        std::cerr << "Failed to start MIDI stream for instance " << instance_id_ << std::endl;
        if (BASS_StreamFree_ptr) {
            BASS_StreamFree_ptr(midi_stream_);
        }
        midi_stream_ = 0;
        return false;
    }
    
    // Set initial volume
    SetVolume(volume_);
    
    initialized_ = true;
    std::cout << "BASS MIDI synth instance " << instance_id_ 
              << " initialized with " << max_voices_ << " max voices" << std::endl;
    return true;
}

void BASSMIDISynth::Cleanup() {
    if (initialized_) {
        if (midi_stream_ != 0 && BASS_StreamFree_ptr) {
            BASS_StreamFree_ptr(midi_stream_);
            midi_stream_ = 0;
        }
        
        current_voices_.store(0);
        initialized_ = false;
        std::cout << "BASS MIDI synth instance " << instance_id_ << " cleaned up" << std::endl;
    }
}

bool BASSMIDISynth::QueueMIDICommand(uint32_t cmd) {
    if (!initialized_) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(queue_mutex_);
    midi_queue_.push_back(cmd);
    return true;
}

void BASSMIDISynth::ProcessMIDIQueue() {
    if (!initialized_ || !BASS_MIDI_StreamEvent_ptr) {
        return;
    }

    std::vector<uint32_t> commands_to_process;
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        commands_to_process.swap(midi_queue_);
    }

    if (!commands_to_process.empty()) {
        std::cout << "BASSMIDISynth instance " << instance_id_ << " processing " << commands_to_process.size() << " MIDI commands" << std::endl;
    }

    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (uint32_t cmd : commands_to_process) {
        uint8_t status = cmd & 0xFF;
        uint8_t note = (cmd >> 8) & 0xFF;
        uint8_t velocity = (cmd >> 16) & 0xFF;
        
        uint8_t channel = status & 0x0F;
        uint8_t status_nibble = status & 0xF0;
        
        switch (status_nibble) {
            case 0x90: // Note On
                if (velocity > 0) {
                    unsigned int midi_param = MAKEWORD(note, velocity);
                    std::cout << "Instance " << instance_id_ << " Note On: note=" << (int)note << ", velocity=" << (int)velocity << std::endl;
                    bool result = BASS_MIDI_StreamEvent_ptr(midi_stream_, channel, MIDI_EVENT_NOTE, midi_param);
                    std::cout << "BASS_MIDI_StreamEvent result: " << (result ? "success" : "failed") << std::endl;
                } else {
                    // Note On with velocity 0 = Note Off
                    unsigned int midi_param = MAKEWORD(note, 0);
                    std::cout << "Instance " << instance_id_ << " Note Off (via Note On): note=" << (int)note << std::endl;
                    BASS_MIDI_StreamEvent_ptr(midi_stream_, channel, MIDI_EVENT_NOTE, midi_param);
                }
                break;
                
            case 0x80: // Note Off
                {
                    unsigned int midi_param = MAKEWORD(note, 0);
                    BASS_MIDI_StreamEvent_ptr(midi_stream_, channel, MIDI_EVENT_NOTE, midi_param);
                }
                break;
                
            case 0xB0: // Control Change
                {
                    unsigned int midi_param = MAKEWORD(note, velocity); // note=controller, velocity=value
                    BASS_MIDI_StreamEvent_ptr(midi_stream_, channel, MIDI_EVENT_CONTROL, midi_param);
                }
                break;
                
            case 0xC0: // Program Change
                BASS_MIDI_StreamEvent_ptr(midi_stream_, channel, MIDI_EVENT_PROGRAM, note);
                break;
                
            default:
                // Handle other MIDI events as needed
                break;
        }
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    last_render_time_ = end_time - start_time;
}

void BASSMIDISynth::SetVolume(float volume) {
    volume_ = std::max(0.0f, std::min(1.0f, volume));
    
    if (initialized_ && BASS_ChannelSetAttribute_ptr) {
        BASS_ChannelSetAttribute_ptr(midi_stream_, BASS_ATTRIB_VOL, volume_);
    }
}

void BASSMIDISynth::SetBASSFunctionPointers(
    decltype(&BASS_MIDI_StreamCreate) create_ptr,
    decltype(&BASS_MIDI_StreamEvent) event_ptr,
    decltype(&BASS_ChannelPlay) play_ptr,
    decltype(&BASS_ChannelSetAttribute) attr_ptr,
    decltype(&BASS_StreamFree) free_ptr,
    decltype(&BASS_MIDI_StreamSetFonts) set_fonts_ptr
) {
    BASS_MIDI_StreamCreate_ptr = create_ptr;
    BASS_MIDI_StreamEvent_ptr = event_ptr;
    BASS_ChannelPlay_ptr = play_ptr;
    BASS_ChannelSetAttribute_ptr = attr_ptr;
    BASS_StreamFree_ptr = free_ptr;
    BASS_MIDI_StreamSetFonts_ptr = set_fonts_ptr;
}

// BASSMultiSynth implementation
BASSMultiSynth::BASSMultiSynth()
    : initialized_(false)
    , soundfont_(0)
    , master_volume_(0.8f)
    , piano_keyboard_(nullptr)
    , BASS_MIDI_StreamCreate_ptr_(nullptr)
    , BASS_MIDI_StreamEvent_ptr_(nullptr)
    , BASS_ChannelPlay_ptr_(nullptr)
    , BASS_ChannelSetAttribute_ptr_(nullptr)
    , BASS_ChannelGetAttribute_ptr_(nullptr)
    , BASS_StreamFree_ptr_(nullptr)
    , BASS_MIDI_FontInit_ptr_(nullptr)
    , BASS_MIDI_StreamSetFonts_ptr_(nullptr)
{
    last_stats_update_ = std::chrono::high_resolution_clock::now();
}

BASSMultiSynth::~BASSMultiSynth() {
    Cleanup();
}

bool BASSMultiSynth::Initialize(uint32_t max_total_voices, uint32_t num_instances, HSOUNDFONT soundfont) {
    if (initialized_) {
        return true;
    }
    
    if (num_instances == 0 || max_total_voices == 0) {
        std::cerr << "Invalid parameters for BASSMultiSynth initialization" << std::endl;
        return false;
    }
    
    if (!BASS_MIDI_StreamCreate_ptr_ || !BASS_MIDI_StreamEvent_ptr_) {
        std::cerr << "BASS function pointers not set" << std::endl;
        return false;
    }
    
    soundfont_ = soundfont;
    
    // Calculate voice distribution (similar to Rust implementation)
    uint32_t base_voice_count = max_total_voices / num_instances;
    max_voices_per_instance_.resize(num_instances, base_voice_count);
    
    // Distribute remaining voices
    uint32_t remaining_voices = max_total_voices % num_instances;
    for (uint32_t i = 0; i < remaining_voices; ++i) {
        max_voices_per_instance_[i]++;
    }
    
    // Set function pointers for BASSMIDISynth
    BASSMIDISynth::SetBASSFunctionPointers(
        BASS_MIDI_StreamCreate_ptr_,
        BASS_MIDI_StreamEvent_ptr_,
        BASS_ChannelPlay_ptr_,
        BASS_ChannelSetAttribute_ptr_,
        BASS_StreamFree_ptr_,
        BASS_MIDI_StreamSetFonts_ptr_
    );
    
    // Create synth instances
    synths_.reserve(num_instances);
    note_counts_.resize(num_instances);

    for (uint32_t i = 0; i < num_instances; ++i) {
        // Initialize atomic counter
        note_counts_[i] = std::make_unique<std::atomic<uint32_t>>(0);

        if (max_voices_per_instance_[i] > 0) {
            auto synth = std::make_unique<BASSMIDISynth>();

            if (!synth->Initialize(i, max_voices_per_instance_[i], soundfont_)) {
                std::cerr << "Failed to initialize synth instance " << i << std::endl;
                Cleanup();
                return false;
            }

            synth->SetVolume(master_volume_.load());
            synths_.push_back(std::move(synth));
        }
    }
    
    initialized_ = true;
    std::cout << "BASSMultiSynth initialized with " << synths_.size()
              << " instances and " << max_total_voices << " total voices" << std::endl;
    return true;
}

void BASSMultiSynth::Cleanup() {
    if (!initialized_) {
        return;
    }

    // Stop all synth instances
    for (auto& synth : synths_) {
        synth->Cleanup();
    }
    synths_.clear();

    // Clear note mapping
    {
        std::lock_guard<std::mutex> lock(note_map_mutex_);
        note_map_.clear();
    }

    note_counts_.clear();
    max_voices_per_instance_.clear();

    initialized_ = false;
    std::cout << "BASSMultiSynth cleaned up" << std::endl;
}

void BASSMultiSynth::QueueMIDICommand(uint32_t cmd) {
    if (!initialized_) {
        return;
    }

    uint8_t status = cmd & 0xFF;
    uint8_t note = (cmd >> 8) & 0xFF;
    uint8_t velocity = (cmd >> 16) & 0xFF;

    uint8_t channel = status & 0x0F;
    uint8_t status_nibble = status & 0xF0;

    switch (status_nibble) {
        case 0x90: // Note On
            if (velocity == 0) {
                NoteOff(channel, note, velocity);
            } else {
                NoteOn(channel, note, velocity);
            }
            break;

        case 0x80: // Note Off
            NoteOff(channel, note, velocity);
            break;

        case 0xA0: // Aftertouch
        case 0xB0: // Control Change
        case 0xC0: // Program Change
        case 0xD0: // Channel Pressure
        case 0xE0: // Pitch Bend
            // Send to all instances
            for (auto& synth : synths_) {
                synth->QueueMIDICommand(cmd);
            }
            break;

        default:
            break;
    }
}

void BASSMultiSynth::NoteOn(uint8_t channel, uint8_t note, uint8_t velocity) {
    std::cout << "BASSMultiSynth::NoteOn called: channel=" << (int)channel << ", note=" << (int)note << ", velocity=" << (int)velocity << std::endl;

    if (!initialized_) {
        std::cout << "BASSMultiSynth not initialized!" << std::endl;
        return;
    }

    // Find the instance with the least load (similar to Rust implementation)
    size_t best_instance = SelectBestInstance();
    std::cout << "Selected instance: " << best_instance << " out of " << synths_.size() << " instances" << std::endl;

    if (best_instance < synths_.size()) {
        NoteKey note_key = {channel, note};

        // Create MIDI command
        uint32_t cmd = 0x90 | channel | (note << 8) | (velocity << 16);
        std::cout << "Created MIDI command: 0x" << std::hex << cmd << std::dec << std::endl;

        // Queue command to selected instance
        bool queued = synths_[best_instance]->QueueMIDICommand(cmd);
        std::cout << "MIDI command queued: " << (queued ? "success" : "failed") << std::endl;

        // Update note mapping
        {
            std::lock_guard<std::mutex> lock(note_map_mutex_);
            note_map_[note_key].push_back(best_instance);
        }

        // Update voice count
        note_counts_[best_instance]->fetch_add(1);
        synths_[best_instance]->IncrementVoices();

        // Notify piano keyboard for visual feedback
        NotifyMIDIKeyPressed(note, true, channel);
    } else {
        std::cout << "No valid synth instance found!" << std::endl;
    }
}

void BASSMultiSynth::NoteOff(uint8_t channel, uint8_t note, uint8_t velocity) {
    NoteKey note_key = {channel, note};

    std::lock_guard<std::mutex> lock(note_map_mutex_);
    auto it = note_map_.find(note_key);

    if (it != note_map_.end() && !it->second.empty()) {
        // Get the last instance that played this note
        size_t instance_idx = it->second.back();
        it->second.pop_back();

        // Remove mapping if no more instances
        if (it->second.empty()) {
            note_map_.erase(it);
        }

        // Create MIDI command
        uint32_t cmd = 0x80 | channel | (note << 8) | (velocity << 16);

        // Queue command to the instance
        if (instance_idx < synths_.size()) {
            synths_[instance_idx]->QueueMIDICommand(cmd);

            // Update voice count
            if (note_counts_[instance_idx]->load() > 0) {
                note_counts_[instance_idx]->fetch_sub(1);
            }
            synths_[instance_idx]->DecrementVoices();
        }

        // Notify piano keyboard for visual feedback
        NotifyMIDIKeyPressed(note, false, channel);
    }
}

void BASSMultiSynth::SendControlChange(uint8_t channel, uint8_t controller, uint8_t value) {
    uint32_t cmd = 0xB0 | channel | (controller << 8) | (value << 16);

    // Send to all instances
    for (auto& synth : synths_) {
        synth->QueueMIDICommand(cmd);
    }
}

void BASSMultiSynth::SendProgramChange(uint8_t channel, uint8_t program) {
    uint32_t cmd = 0xC0 | channel | (program << 8);

    // Send to all instances
    for (auto& synth : synths_) {
        synth->QueueMIDICommand(cmd);
    }
}

void BASSMultiSynth::StopAllNotes() {
    // Send All Notes Off to all instances
    for (uint8_t channel = 0; channel < 16; ++channel) {
        uint32_t cmd = 0xB0 | channel | (123 << 8) | (0 << 16); // CC 123 = All Notes Off

        for (auto& synth : synths_) {
            synth->QueueMIDICommand(cmd);
        }
    }

    // Clear note mapping
    {
        std::lock_guard<std::mutex> lock(note_map_mutex_);
        note_map_.clear();
    }

    // Reset voice counts
    for (auto& count : note_counts_) {
        if (count) {
            count->store(0);
        }
    }

    for (auto& synth : synths_) {
        synth->ResetVoices();
    }

    std::cout << "All notes stopped in BASSMultiSynth" << std::endl;
}

void BASSMultiSynth::ProcessAudio() {
    if (!initialized_) {
        return;
    }

    // Process MIDI queues for all instances
    for (auto& synth : synths_) {
        synth->ProcessMIDIQueue();
    }

    // Update performance statistics periodically
    UpdatePerformanceStats();
}

uint32_t BASSMultiSynth::GetPolyphony() const {
    uint32_t total = 0;

    // Use BASS to get actual polyphony from each stream
    for (const auto& synth : synths_) {
        if (synth->IsInitialized() && BASS_ChannelGetAttribute_ptr_) {
            float voices = 0.0f;
            HSTREAM stream = synth->GetMIDIStream();
            if (stream != 0 && BASS_ChannelGetAttribute_ptr_(stream, BASS_ATTRIB_MIDI_VOICES_ACTIVE, &voices)) {
                total += static_cast<uint32_t>(voices);
            } else {
                // Fallback to our own tracking
                total += synth->GetCurrentVoices();
            }
        } else {
            // Fallback to our own tracking
            total += synth->GetCurrentVoices();
        }
    }
    return total;
}

uint32_t BASSMultiSynth::GetMaxPolyphony() const {
    uint32_t total = 0;
    for (const auto& synth : synths_) {
        total += synth->GetMaxVoices();
    }
    return total;
}

float BASSMultiSynth::GetRenderingTimeRatio() const {
    if (synths_.empty()) {
        return 0.0f;
    }

    double total_time = 0.0;
    for (const auto& synth : synths_) {
        total_time += synth->GetLastRenderTime().count();
    }

    return static_cast<float>(total_time / synths_.size() * 1000.0); // Convert to milliseconds
}

void BASSMultiSynth::SetMasterVolume(float volume) {
    master_volume_.store(std::max(0.0f, std::min(1.0f, volume)));

    // Update volume for all instances
    for (auto& synth : synths_) {
        synth->SetVolume(master_volume_.load());
    }
}

bool BASSMultiSynth::LoadSoundfont(const std::string& soundfont_path) {
    if (!BASS_MIDI_FontInit_ptr_) {
        std::cerr << "BASS_MIDI_FontInit function not available" << std::endl;
        return false;
    }

    // Free existing soundfont
    if (soundfont_ != 0) {
        // Note: We would need BASS_MIDI_FontFree here
        soundfont_ = 0;
    }

    // Load new soundfont
    soundfont_ = BASS_MIDI_FontInit_ptr_(soundfont_path.c_str(), 0);
    if (soundfont_ == 0) {
        std::cerr << "Failed to load soundfont: " << soundfont_path << std::endl;
        return false;
    }

    current_soundfont_path_ = soundfont_path;

    // Reinitialize all instances with new soundfont
    if (initialized_) {
        // For now, we'll just store the soundfont for new instances
        // Full reinitialization would require stopping all audio
    }

    std::cout << "Soundfont loaded for BASSMultiSynth: " << soundfont_path << std::endl;
    return true;
}

void BASSMultiSynth::SetPianoKeyboard(PianoKeyboard* piano_keyboard) {
    piano_keyboard_ = piano_keyboard;
}

BASSMultiSynth::PerformanceStats BASSMultiSynth::GetPerformanceStats() const {
    PerformanceStats stats;

    stats.total_voices = GetPolyphony();
    stats.max_voices = GetMaxPolyphony();
    stats.avg_rendering_time_ms = GetRenderingTimeRatio();

    // Collect per-instance statistics
    stats.instance_voices.reserve(synths_.size());
    stats.instance_loads.reserve(synths_.size());

    for (const auto& synth : synths_) {
        stats.instance_voices.push_back(synth->GetCurrentVoices());

        float load = 0.0f;
        if (synth->GetMaxVoices() > 0) {
            load = static_cast<float>(synth->GetCurrentVoices()) / synth->GetMaxVoices();
        }
        stats.instance_loads.push_back(load);
    }

    return stats;
}

size_t BASSMultiSynth::SelectBestInstance() const {
    if (synths_.empty()) {
        return 0;
    }

    // Find instance with minimum load (similar to Rust implementation)
    size_t best_instance = 0;
    uint32_t min_voices = UINT32_MAX;

    for (size_t i = 0; i < synths_.size(); ++i) {
        uint32_t current_voices = note_counts_[i] ? note_counts_[i]->load() : 0;
        uint32_t max_voices = synths_[i]->GetMaxVoices();

        // Only consider instances that have capacity
        if (current_voices < max_voices && current_voices < min_voices) {
            min_voices = current_voices;
            best_instance = i;
        }
    }

    return best_instance;
}

void BASSMultiSynth::NotifyMIDIKeyPressed(uint8_t note, bool pressed) {
    // Call the overloaded version with channel -1 (no specific channel)
    NotifyMIDIKeyPressed(note, pressed, 255); // Use 255 as invalid channel
}

void BASSMultiSynth::NotifyMIDIKeyPressed(uint8_t note, bool pressed, uint8_t channel) {
    if (piano_keyboard_) {
        piano_keyboard_->SetMIDIKeyPressed(note, pressed, channel);
    }
}

void BASSMultiSynth::UpdatePerformanceStats() {
    auto now = std::chrono::high_resolution_clock::now();
    std::lock_guard<std::mutex> lock(stats_mutex_);

    // Update statistics every second
    if (now - last_stats_update_ > std::chrono::seconds(1)) {
        last_stats_update_ = now;
        // Additional performance tracking could be added here
    }
}

void BASSMultiSynth::SetBASSFunctionPointers(
    decltype(&BASS_MIDI_StreamCreate) create_ptr,
    decltype(&BASS_MIDI_StreamEvent) event_ptr,
    decltype(&BASS_ChannelPlay) play_ptr,
    decltype(&BASS_ChannelSetAttribute) attr_ptr,
    decltype(&BASS_ChannelGetAttribute) get_attr_ptr,
    decltype(&BASS_StreamFree) free_ptr,
    decltype(&BASS_MIDI_FontInit) font_init_ptr,
    decltype(&BASS_MIDI_StreamSetFonts) set_fonts_ptr
) {
    BASS_MIDI_StreamCreate_ptr_ = create_ptr;
    BASS_MIDI_StreamEvent_ptr_ = event_ptr;
    BASS_ChannelPlay_ptr_ = play_ptr;
    BASS_ChannelSetAttribute_ptr_ = attr_ptr;
    BASS_ChannelGetAttribute_ptr_ = get_attr_ptr;
    BASS_StreamFree_ptr_ = free_ptr;
    BASS_MIDI_FontInit_ptr_ = font_init_ptr;
    BASS_MIDI_StreamSetFonts_ptr_ = set_fonts_ptr;
}
